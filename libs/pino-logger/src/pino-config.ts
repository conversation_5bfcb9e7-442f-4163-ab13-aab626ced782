import { randomUUID } from 'node:crypto';
import { IncomingMessage } from 'node:http';
import { LoggerOptions } from 'pino';
import { Options } from 'pino-http';
import { ConfigService, isDevelopment } from '@libs/common/config';

type PinoHttpOptionsWithTransport = Options & Pick<LoggerOptions, 'transport'>;

export const usePinoHttpOptions = (options: {
  configService: ConfigService;
}): PinoHttpOptionsWithTransport => {
  const { configService } = options;

  const baseOptions: Options = {
    useLevel: configService.get('LOG_LEVEL'),
    genReqId: (req: IncomingMessage) => req.headers['x-correlation-id'] || randomUUID(),
  };

  // Use pino-pretty for development
  if (isDevelopment()) {
    return {
      ...baseOptions,
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'SYS:standard',
          colorize: true,
          singleLine: true,
          ignore:
            'pid,hostname,context,req.headers,req.params,req.query,req.remoteAddress,req.remotePort,res',
        },
      },
    };
  }

  return baseOptions;
};
