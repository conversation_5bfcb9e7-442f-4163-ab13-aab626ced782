import { NestFactory } from '@nestjs/core';
import { cleanupOpenApiDoc } from 'nestjs-zod';
import { ConfigService } from '@libs/common/config';
import { Logger, LoggerErrorInterceptor } from '@libs/pino-logger';
import { DocumentBuilder, SwaggerModule } from '@libs/swagger';
import { AppModule } from './app/app.module';

// TODO: go on with configuring new version of https://github.com/BenLorantfy/nestjs-zod#readme

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  // app.enableShutdownHooks();

  const config = app.get(ConfigService);

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Configure logger
  const logger = app.get(Logger);
  app.useLogger(logger);
  app.useGlobalInterceptors(new LoggerErrorInterceptor());

  const isSwaggerEnabled = config.get('SWAGGER_ENABLED');
  if (isSwaggerEnabled) {
    const openApiConfig = new DocumentBuilder()
      .setTitle('API')
      .setVersion('0.0.0')
      // .addBearerAuth({ type: 'http', scheme: 'bearer' }, ACCESS_TOKEN_SECURITY_KEY)
      .build();

    const openApi = SwaggerModule.createDocument(app, openApiConfig);
    // to see the webpage open: /swagger
    SwaggerModule.setup('swagger', app, cleanupOpenApiDoc(openApi), {
      jsonDocumentUrl: 'swagger/json',
    });
  }

  await app.listen(config.getOrThrow('PORT'));

  logger.log(`
    🚀 Application is running on: ${await app.getUrl()}
    ${isSwaggerEnabled ? `Swagger UI: ${await app.getUrl()}/swagger` : 'Swagger is disabled'}
    Node env: ${process.env.NODE_ENV}
  `);
}

void bootstrap();
