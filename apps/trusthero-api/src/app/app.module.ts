import { Module } from '@nestjs/common';
import { APP_PIPE } from '@nestjs/core';
import { ZodValidationPipe } from 'nestjs-zod';
import { ConfigModule, ConfigService } from '@libs/common/config';
import { LoggerModule, usePinoHttpOptions } from '@libs/pino-logger';
import { EnvironmentVariables, EnvVariablesSchema } from '../config';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      schema: EnvVariablesSchema,
    }),

    LoggerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService<EnvironmentVariables>) => {
        const pinoHttp = usePinoHttpOptions({
          configService: config,
        });
        return { pinoHttp };
      },
    }),
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },

    AppService,
  ],
})
export class AppModule {}
