{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@libs/common/api": ["libs/common/src/api/index.ts"], "@libs/common/config": ["libs/common/src/config/index.ts"], "@libs/pino-logger": ["libs/pino-logger/src/index.ts"], "@libs/swagger": ["libs/swagger/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}